<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="260dp"
    android:height="260dp"
    android:viewportWidth="260"
    android:viewportHeight="260">

    <path
        android:fillColor="#ffffff"
        android:pathData="M 43.3333 34.3333 C 48.********** 34.3333 52.3333 38.********** 52.3333 43.3333 C 52.3333 48.********** 48.********** 52.3333 43.3333 52.3333 C 38.********** 52.3333 34.3333 48.********** 34.3333 43.3333 C 34.3333 38.********** 38.********** 34.3333 43.3333 34.3333 Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M 43.3333 121 C 48.********** 121 52.3333 125.029437252 52.3333 130 C 52.3333 134.970562748 48.********** 139 43.3333 139 C 38.********** 139 34.3333 134.970562748 34.3333 130 C 34.3333 125.029437252 38.********** 121 43.3333 121 Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M 43.3333 207.6667 C 48.********** 207.6667 52.3333 211.696137252 52.3333 216.6667 C 52.3333 221.637262748 48.********** 225.6667 43.3333 225.6667 C 38.********** 225.6667 34.3333 221.637262748 34.3333 216.6667 C 34.3333 211.696137252 38.********** 207.6667 43.3333 207.6667 Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M 130 34.3333 C 134.970562748 34.3333 139 38.********** 139 43.3333 C 139 48.********** 134.970562748 52.3333 130 52.3333 C 125.029437252 52.3333 121 48.********** 121 43.3333 C 121 38.********** 125.029437252 34.3333 130 34.3333 Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M 130 121 C 134.970562748 121 139 125.029437252 139 130 C 139 134.970562748 134.970562748 139 130 139 C 125.029437252 139 121 134.970562748 121 130 C 121 125.029437252 125.029437252 121 130 121 Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M 130 207.6667 C 134.970562748 207.6667 139 211.696137252 139 216.6667 C 139 221.637262748 134.970562748 225.6667 130 225.6667 C 125.029437252 225.6667 121 221.637262748 121 216.6667 C 121 211.696137252 125.029437252 207.6667 130 207.6667 Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M 216.6667 34.3333 C 221.637262748 34.3333 225.6667 38.********** 225.6667 43.3333 C 225.6667 48.********** 221.637262748 52.3333 216.6667 52.3333 C 211.696137252 52.3333 207.6667 48.********** 207.6667 43.3333 C 207.6667 38.********** 211.696137252 34.3333 216.6667 34.3333 Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M 216.6667 121 C 221.637262748 121 225.6667 125.029437252 225.6667 130 C 225.6667 134.970562748 221.637262748 139 216.6667 139 C 211.696137252 139 207.6667 134.970562748 207.6667 130 C 207.6667 125.029437252 211.696137252 121 216.6667 121 Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M 216.6667 207.6667 C 221.637262748 207.6667 225.6667 211.696137252 225.6667 216.6667 C 225.6667 221.637262748 221.637262748 225.6667 216.6667 225.6667 C 211.696137252 225.6667 207.6667 221.637262748 207.6667 216.6667 C 207.6667 211.696137252 211.696137252 207.6667 216.6667 207.6667 Z" />
</vector>
