<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:minWidth="280dp"
    android:background="@drawable/bg_rounded_card">
    
    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/repair_libs_dialog_title"
        android:textSize="18sp"
        android:textColor="@color/on_primary"
        android:gravity="center"
        android:layout_marginBottom="16dp"/>
        
    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="100"
        android:progress="0"/>
        
    <TextView
        android:id="@+id/progress_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="0%"
        android:gravity="center"
        android:textColor="@color/on_primary"
        android:layout_marginTop="8dp"/>
        
</LinearLayout>