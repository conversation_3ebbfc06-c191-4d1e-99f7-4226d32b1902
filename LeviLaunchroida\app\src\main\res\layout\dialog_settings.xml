<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

<LinearLayout
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:minWidth="480dp"
    android:minHeight="320dp"
    android:background="@drawable/bg_rounded_card">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@drawable/ic_settings"/>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="12dp">
            <TextView
                android:id="@+id/settings_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="20sp"
                android:textStyle="bold"
                android:text="@string/settings_title"/>
            <TextView
                android:id="@+id/settings_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#888888"
                android:textSize="14sp"
                android:text="@string/settings_desc"/>
        </LinearLayout>
    </LinearLayout>

    <Space android:layout_width="match_parent" android:layout_height="12dp"/>

    <LinearLayout
        android:id="@+id/settings_items"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"/>

</LinearLayout>
</ScrollView>