<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<animated-selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="false">
        <shape
            android:shape="ring"
            android:innerRadius="2dp"
            android:thickness="2dp"
            android:useLevel="false"
            android:tint="?attr/colorControlNormal"
            android:opticalInsetLeft="3dp"
            android:opticalInsetRight="3dp">
            <solid
                android:color="@color/white" />
            <size
                android:width="18dp"
                android:height="18dp" />
        </shape>
    </item>
    <item
        android:id="@+id/pressed"
        android:state_pressed="true"
        android:drawable="@drawable/seekbar_thumb_pressed_to_unpressed" />
    <item
        android:id="@+id/unpressed"
        android:drawable="@drawable/seekbar_thumb_unpressed_to_pressed" />
    <transition
        android:fromId="@+id/unpressed"
        android:toId="@+id/pressed"
        android:drawable="@drawable/seekbar_thumb_unpressed_to_pressed_animation" />
    <transition
        android:fromId="@+id/pressed"
        android:toId="@+id/unpressed"
        android:drawable="@drawable/seekbar_thumb_pressed_to_unpressed_animation" />
</animated-selector>
