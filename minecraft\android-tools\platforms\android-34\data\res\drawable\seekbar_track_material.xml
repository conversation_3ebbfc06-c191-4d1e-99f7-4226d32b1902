<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:id="@id/background"
          android:gravity="center_vertical|fill_horizontal">
        <shape android:shape="rectangle"
               android:tint="?attr/colorProgressBackgroundNormal">
            <corners android:radius="?attr/progressBarCornerRadius" />
            <size android:height="@dimen/seekbar_track_background_height_material" />
            <solid android:color="@color/white_disabled_material" />
        </shape>
    </item>
    <item android:id="@id/secondaryProgress"
          android:gravity="center_vertical|fill_horizontal">
        <scale android:scaleWidth="100%">
            <selector>
                <item android:state_enabled="false"
                      android:drawable="@color/transparent" />
                <item>
                    <shape android:shape="rectangle"
                           android:tint="?attr/colorControlActivated">
                        <corners android:radius="?attr/progressBarCornerRadius" />
                        <size android:height="@dimen/seekbar_track_progress_height_material" />
                        <solid android:color="@color/white_disabled_material" />
                    </shape>
                </item>
            </selector>
        </scale>
    </item>
    <item android:id="@id/progress"
          android:gravity="center_vertical|fill_horizontal">
        <scale android:scaleWidth="100%">
            <selector>
                <item android:state_enabled="false"
                      android:drawable="@color/transparent" />
                <item>
                    <shape android:shape="rectangle"
                           android:tint="?attr/colorControlActivated">
                        <corners android:radius="?attr/progressBarCornerRadius" />
                        <size android:height="@dimen/seekbar_track_progress_height_material" />
                        <solid android:color="@color/white" />
                    </shape>
                </item>
            </selector>
        </scale>
    </item>
</layer-list>