<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:height="10dp"
    android:width="360dp"
    android:viewportHeight="10"
    android:viewportWidth="360" >
    <group
        android:name="progress_group"
        android:translateX="180"
        android:translateY="5" >
        <path
            android:name="background_track"
            android:pathData="M -180.0,-1.0 l 360.0,0 l 0,2.0 l -360.0,0 Z"
            android:fillColor="?attr/colorControlActivated"
            android:fillAlpha="?attr/disabledAlpha"/>
        <group
            android:name="rect2_grp"
            android:translateX="-197.60001"
            android:scaleX="0.1" >
            <path
                android:name="rect2"
                android:pathData="M -144.0,-1.0 l 288.0,0 l 0,2.0 l -288.0,0 Z"
                android:fillColor="?attr/colorControlActivated" />
        </group>
        <group
            android:name="rect1_grp"
            android:translateX="-522.59998"
            android:scaleX="0.1" >
            <path
                android:name="rect1"
                android:pathData="M -144.0,-1.0 l 288.0,0 l 0,2.0 l -288.0,0 Z"
                android:fillColor="?attr/colorControlActivated" />
        </group>
    </group>
</vector>
