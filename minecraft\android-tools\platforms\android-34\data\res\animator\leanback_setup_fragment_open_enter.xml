<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<set xmlns:android="http://schemas.android.com/apk/res/android" >
   <objectAnimator
        android:propertyName="alpha"
        android:valueType="floatType"
        android:valueFrom="@dimen/leanback_setup_alpha_forward_in_content_start"
        android:valueTo="@dimen/leanback_setup_alpha_forward_in_content_end"
        android:duration="@integer/leanback_setup_alpha_forward_in_content_duration"
        android:startOffset="@integer/leanback_setup_alpha_forward_in_content_delay"/>
    <objectAnimator
        android:propertyName="x"
        android:valueType="floatType"
        android:valueFrom="@dimen/leanback_setup_translation_forward_in_content_start"
        android:valueTo="@dimen/leanback_setup_translation_forward_in_content_end"
        android:duration="@integer/leanback_setup_translation_forward_in_content_duration"
        android:startOffset="@integer/leanback_setup_translation_forward_in_content_delay" />
</set>
