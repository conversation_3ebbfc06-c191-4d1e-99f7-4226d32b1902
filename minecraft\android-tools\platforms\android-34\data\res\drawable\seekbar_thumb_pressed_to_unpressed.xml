<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<vector
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:name="seekbar_thumb_pressed_to_unpressed"
    android:width="18dp"
    android:height="18dp"
    android:viewportWidth="18"
    android:viewportHeight="18"
    android:tint="?attr/colorControlActivated"
    android:opticalInsetLeft="6dp"
    android:opticalInsetRight="6dp">
    <group
        android:name="thumb"
        android:translateX="9"
        android:translateY="9"
        android:scaleX="1.5"
        android:scaleY="1.5">
        <path
            android:name="thumb_path"
            android:fillColor="#FF000000"
            android:pathData="M 0.0,-6.0 c 3.**********,0.0 6.0,2.********** 6.0,6.0 c 0.0,3.********** -2.**********,6.0 -6.0,6.0 c -3.**********,0.0 -6.0,-2.********** -6.0,-6.0 c 0.0,-3.********** 2.**********,-6.0 6.0,-6.0 Z" />
    </group>
</vector>
