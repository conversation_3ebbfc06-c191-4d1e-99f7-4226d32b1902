<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2022 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
        android:color="@color/resolver_accent_ripple">

    <item android:id="@android:id/mask">
        <shape android:shape="rectangle">
            <corners android:radius="12dp" />
            <solid android:color="@color/resolver_accent_ripple" />
        </shape>
    </item>

    <item>
        <inset
            android:insetLeft="0dp"
            android:insetTop="6dp"
            android:insetRight="0dp"
            android:insetBottom="6dp">
            <shape android:shape="rectangle">
                <corners android:radius="12dp" />
                <solid android:color="@color/resolver_profile_tab_selected_bg" />
            </shape>
        </inset>
    </item>

</ripple>