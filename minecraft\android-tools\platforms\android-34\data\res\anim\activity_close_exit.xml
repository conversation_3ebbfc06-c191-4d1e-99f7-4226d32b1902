<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2009, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:shareInterpolator="false">

    <alpha
        android:fromAlpha="1.0"
        android:toAlpha="0.0"
        android:fillEnabled="true"
        android:fillBefore="true"
        android:fillAfter="true"
        android:interpolator="@interpolator/linear"
        android:startOffset="35"
        android:duration="83" />

    <translate
        android:fromXDelta="0"
        android:toXDelta="10%"
        android:fillEnabled="true"
        android:fillBefore="true"
        android:fillAfter="true"
        android:interpolator="@interpolator/fast_out_extra_slow_in"
        android:startOffset="0"
        android:duration="450" />

    <extend
        android:fromExtendLeft="10%"
        android:fromExtendTop="0"
        android:fromExtendRight="0"
        android:fromExtendBottom="0"
        android:toExtendLeft="10%"
        android:toExtendTop="0"
        android:toExtendRight="0"
        android:toExtendBottom="0"
        android:interpolator="@interpolator/fast_out_extra_slow_in"
        android:startOffset="0"
        android:duration="450" />
</set>
